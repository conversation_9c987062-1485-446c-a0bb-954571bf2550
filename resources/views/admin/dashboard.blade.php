<x-layouts.app title="Admin Dashboard">
    <flux:header>
        <flux:heading size="xl">Admin Dashboard</flux:heading>
        <flux:subheading>Manage users, properties, and system settings</flux:subheading>
    </flux:header>

    <flux:main class="space-y-6">
        <!-- Overview Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <flux:card>
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="lg" class="text-blue-600">{{ $totalUsers }}</flux:heading>
                        <flux:subheading>Total Users</flux:subheading>
                    </div>
                    <flux:icon.users class="size-8 text-blue-500" />
                </div>
            </flux:card>

            <flux:card>
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="lg" class="text-green-600">{{ $totalProperties }}</flux:heading>
                        <flux:subheading>Total Properties</flux:subheading>
                    </div>
                    <flux:icon.building-office class="size-8 text-green-500" />
                </div>
            </flux:card>

            <flux:card>
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="lg" class="text-yellow-600">{{ $publishedProperties }}</flux:heading>
                        <flux:subheading>Published Properties</flux:subheading>
                    </div>
                    <flux:icon.check-circle class="size-8 text-yellow-500" />
                </div>
            </flux:card>

            <flux:card>
                <div class="flex items-center justify-between">
                    <div>
                        <flux:heading size="lg" class="text-red-600">{{ $pendingProperties }}</flux:heading>
                        <flux:subheading>Pending Properties</flux:subheading>
                    </div>
                    <flux:icon.clock class="size-8 text-red-500" />
                </div>
            </flux:card>
        </div>

        <!-- Quick Actions -->
        <flux:card>
            <flux:card.header>
                <flux:heading size="lg">Quick Actions</flux:heading>
                <flux:subheading>Common administrative tasks</flux:subheading>
            </flux:card.header>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <flux:button :href="route('admin.users.index')" variant="primary" size="lg" class="justify-start" wire:navigate>
                    <flux:icon.users class="size-5" />
                    Manage Users
                </flux:button>

                <flux:button :href="route('admin.properties.index')" variant="primary" size="lg" class="justify-start" wire:navigate>
                    <flux:icon.building-office class="size-5" />
                    Manage Properties
                </flux:button>

                <flux:button :href="route('properties.index')" variant="outline" size="lg" class="justify-start" wire:navigate>
                    <flux:icon.magnifying-glass class="size-5" />
                    View Public Listings
                </flux:button>
            </div>
        </flux:card>
    </flux:main>
</x-layouts.app>
