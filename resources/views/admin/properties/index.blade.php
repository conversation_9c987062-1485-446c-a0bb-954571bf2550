<x-layouts.app title="Property Management">
    <flux:header>
        <flux:heading size="xl">Property Management</flux:heading>
        <flux:subheading>Manage all property listings and their status</flux:subheading>
    </flux:header>

    <flux:main class="space-y-6">
        @if (session('success'))
            <flux:banner variant="success" :closable="true">
                {{ session('success') }}
            </flux:banner>
        @endif

        <!-- Search and Filters -->
        <flux:card>
            <flux:card.header>
                <flux:heading size="lg">Search & Filter</flux:heading>
            </flux:card.header>

            <form action="{{ route('admin.properties.index') }}" method="GET">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <flux:field>
                        <flux:label>Keywords</flux:label>
                        <flux:input name="keywords" value="{{ request('keywords') }}" placeholder="Search properties..." />
                    </flux:field>

                    <flux:field>
                        <flux:label>Status</flux:label>
                        <flux:select name="status">
                            <option value="all" {{ request('status') == 'all' ? 'selected' : '' }}>All Statuses</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="sold" {{ request('status') == 'sold' ? 'selected' : '' }}>Sold</option>
                            <option value="rented" {{ request('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                            <option value="under_offer" {{ request('status') == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                        </flux:select>
                    </flux:field>

                    <flux:field class="flex items-end">
                        <flux:button type="submit" variant="primary">
                            <flux:icon.magnifying-glass class="size-4" />
                            Filter
                        </flux:button>
                    </flux:field>
                </div>
            </form>
        </flux:card>

        <!-- Properties Table -->
        <flux:card>
            <flux:card.header>
                <flux:heading size="lg">All Properties</flux:heading>
            </flux:card.header>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lister</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach ($properties as $property)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $property->id }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $property->title }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $property->user->name }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $property->property_type }} ({{ $property->listing_type }})</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $property->currency }} {{ number_format($property->price) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $property->status == 'published' ? 'bg-green-100 text-green-800' : ($property->status == 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                                                {{ ucfirst(str_replace('_', ' ', $property->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('admin.properties.edit', $property->id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                                            <form action="{{ route('admin.properties.destroy', $property->id) }}" method="POST" class="inline-block">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Are you sure you want to delete this property?')">Delete</button>
                                            </form>
                                            <form action="{{ route('admin.properties.updateStatus', $property->id) }}" method="POST" class="inline-block ml-3">
                                                @csrf
                                                @method('PATCH')
                                                <select name="status" onchange="this.form.submit()" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-xs">
                                                    <option value="draft" {{ $property->status == 'draft' ? 'selected' : '' }}>Draft</option>
                                                    <option value="published" {{ $property->status == 'published' ? 'selected' : '' }}>Publish</option>
                                                    <option value="sold" {{ $property->status == 'sold' ? 'selected' : '' }}>Sold</option>
                                                    <option value="rented" {{ $property->status == 'rented' ? 'selected' : '' }}>Rented</option>
                                                    <option value="under_offer" {{ $property->status == 'under_offer' ? 'selected' : '' }}>Under Offer</option>
                                                </select>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                    </table>
            </div>

            <div class="p-6">
                {{ $properties->links() }}
            </div>
        </flux:card>
    </flux:main>
</x-layouts.app>
