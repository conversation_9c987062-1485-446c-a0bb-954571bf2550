<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white">
        <flux:sidebar sticky stashable class="border-e border-zinc-200 bg-zinc-50">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route('dashboard') }}" class="me-5 flex items-center space-x-2 rtl:space-x-reverse" wire:navigate>
                <x-app-logo />
            </a>

            <flux:navlist variant="outline">
                <flux:navlist.group :heading="__('Platform')">
                    <flux:navlist.item icon="layout-grid" :href="route('dashboard')" :current="request()->routeIs('dashboard')" wire:navigate>{{ __('Dashboard') }}</flux:navlist.item>
                    <flux:navlist.item icon="home" :href="route('properties.index')" :current="request()->routeIs('properties.index')" wire:navigate>{{ __('Properties') }}</flux:navlist.item>
                    @auth
                        @if (auth()->user()->role === 'lister')
                            <flux:navlist.item icon="building-office" :href="route('properties.my')" :current="request()->routeIs('properties.my')" wire:navigate>{{ __('My Properties') }}</flux:navlist.item>
                            <flux:navlist.item icon="plus-circle" :href="route('properties.create')" :current="request()->routeIs('properties.create')" wire:navigate>{{ __('Create Listing') }}</flux:navlist.item>
                        @endif
                        @if (auth()->user()->role === 'admin')
                            <flux:navlist.item icon="shield" :href="route('admin.dashboard')" :current="request()->routeIs('admin.*')" wire:navigate>{{ __('Admin Dashboard') }}</flux:navlist.item>
                        @endif
                    @endauth
                </flux:navlist.group>

                @auth
                    @if (auth()->user()->role === 'admin')
                        <flux:navlist.group :heading="__('Administration')">
                            <flux:navlist.item icon="users" :href="route('admin.users.index')" :current="request()->routeIs('admin.users.*')" wire:navigate>{{ __('User Management') }}</flux:navlist.item>
                            <flux:navlist.item icon="building-office" :href="route('admin.properties.index')" :current="request()->routeIs('admin.properties.*')" wire:navigate>{{ __('Property Management') }}</flux:navlist.item>
                        </flux:navlist.group>
                    @endif
                @endauth
            </flux:navlist>

            <flux:spacer />

            <flux:navlist variant="outline">
                <flux:navlist.item icon="folder-git-2" href="https://github.com/laravel/livewire-starter-kit" target="_blank">
                {{ __('Repository') }}
                </flux:navlist.item>

                <flux:navlist.item icon="book-open-text" href="https://laravel.com/docs/starter-kits#livewire" target="_blank">
                {{ __('Documentation') }}
                </flux:navlist.item>
            </flux:navlist>

            <!-- Desktop User Menu -->
            <flux:dropdown position="bottom" align="start">
                <flux:profile
                    :name="auth()->user()->name"
                    :initials="auth()->user()->initials()"
                    icon-trailing="chevrons-up-down"
                />

                <flux:menu class="w-[220px]">
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black"
                                    >
                                        {{ auth()->user()->initials() }}
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                        @if (auth()->user()->role === 'lister')
                            <flux:menu.item :href="route('properties.my')" icon="building-office" wire:navigate>{{ __('My Properties') }}</flux:menu.item>
                            <flux:menu.item :href="route('properties.create')" icon="plus-circle" wire:navigate>{{ __('Create Listing') }}</flux:menu.item>
                        @endif
                        @if (auth()->user()->role === 'admin')
                            <flux:menu.item :href="route('admin.dashboard')" icon="shield" wire:navigate>{{ __('Admin Dashboard') }}</flux:menu.item>
                        @endif
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Mobile User Menu -->
        <flux:header class="lg:hidden">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <flux:spacer />

            <flux:dropdown position="top" align="end">
                <flux:profile
                    :initials="auth()->user()->initials()"
                    icon-trailing="chevron-down"
                />

                <flux:menu>
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black"
                                    >
                                        {{ auth()->user()->initials() }}
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    <span class="truncate font-semibold">{{ auth()->user()->name }}</span>
                                    <span class="truncate text-xs">{{ auth()->user()->email }}</span>
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <flux:menu.radio.group>
                        <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                        @if (auth()->user()->role === 'lister')
                            <flux:menu.item :href="route('properties.my')" icon="building-office" wire:navigate>{{ __('My Properties') }}</flux:menu.item>
                            <flux:menu.item :href="route('properties.create')" icon="plus-circle" wire:navigate>{{ __('Create Listing') }}</flux:menu.item>
                        @endif
                        @if (auth()->user()->role === 'admin')
                            <flux:menu.item :href="route('admin.dashboard')" icon="shield" wire:navigate>{{ __('Admin Dashboard') }}</flux:menu.item>
                        @endif
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        {{ $slot }}

        @fluxScripts
    </body>
</html>
